import { Injectable, Logger } from '@nestjs/common';
import { TagRepository } from '../repositories/tag.repository';
import { Tag } from '../entities/tag.entity';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { AppException } from '@/common/exceptions/app.exception';
import { TODOLISTS_ERROR_CODES } from '../errors/todolists-error.code';
import { CreateTagDto } from '../dto/tag/create-tag.dto';
import { UpdateTagDto } from '../dto/tag/update-tag.dto';
import { TagQueryDto } from '../dto/tag/tag-query.dto';
import { BulkDeleteTagResponseDto } from '../dto/tag/bulk-delete-tag-response.dto';

/**
 * Service xử lý logic nghiệp vụ cho Tag
 */
@Injectable()
export class TagService {
  private readonly logger = new Logger(TagService.name);

  constructor(private readonly tagRepository: TagRepository) {}

  /**
   * Tạo tag mới
   * @param tenantId ID tenant (required for tenant isolation)
   * @param createTagDto Thông tin tag cần tạo
   * @returns Tag đã tạo
   */
  async createTag(tenantId: string, createTagDto: CreateTagDto): Promise<Tag> {
    try {
      // Kiểm tra tag đã tồn tại chưa
      const existingTag = await this.tagRepository.findByName(
        tenantId,
        createTagDto.name,
      );
      if (existingTag) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_ALREADY_EXISTS,
          `Tag với tên "${createTagDto.name}" đã tồn tại`,
        );
      }

      // Tạo tag mới
      return await this.tagRepository.create(tenantId, createTagDto);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo tag: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_CREATION_FAILED,
        `Tạo tag thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách tag có phân trang
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách tag có phân trang
   */
  async findAllTags(
    tenantId: string,
    query: TagQueryDto,
  ): Promise<PaginatedResult<Tag>> {
    try {
      return await this.tagRepository.findAll(
        tenantId,
        query.page,
        query.limit,
        query.search,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách tag: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_FETCH_FAILED,
        `Lấy danh sách tag thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy tất cả tag không phân trang (dùng cho dropdown, select)
   * @param tenantId ID tenant (required for tenant isolation)
   * @param search Từ khóa tìm kiếm (optional)
   * @returns Danh sách tất cả tag
   */
  async getAllTagsWithoutPagination(
    tenantId: string,
    search?: string,
  ): Promise<Tag[]> {
    try {
      return await this.tagRepository.findAllWithoutPagination(tenantId, search);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy tất cả tag: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_FETCH_FAILED,
        `Lấy tất cả tag thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin tag theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @returns Thông tin tag
   */
  async findTagById(tenantId: string, id: number): Promise<Tag> {
    try {
      const tag = await this.tagRepository.findById(tenantId, id);
      if (!tag) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_NOT_FOUND,
          `Không tìm thấy tag với ID ${id}`,
        );
      }
      return tag;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy thông tin tag: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_FETCH_FAILED,
        `Lấy thông tin tag thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật tag
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @param updateTagDto Thông tin cập nhật
   * @returns Tag đã cập nhật
   */
  async updateTag(
    tenantId: string,
    id: number,
    updateTagDto: UpdateTagDto,
  ): Promise<Tag> {
    try {
      // Kiểm tra tag tồn tại
      const existingTag = await this.tagRepository.findById(tenantId, id);
      if (!existingTag) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_NOT_FOUND,
          `Không tìm thấy tag với ID ${id}`,
        );
      }

      // Kiểm tra tên tag đã tồn tại chưa (nếu có thay đổi tên)
      if (updateTagDto.name && updateTagDto.name !== existingTag.name) {
        const tagWithSameName = await this.tagRepository.findByName(
          tenantId,
          updateTagDto.name,
        );
        if (tagWithSameName && tagWithSameName.id !== id) {
          throw new AppException(
            TODOLISTS_ERROR_CODES.TAG_ALREADY_EXISTS,
            `Tag với tên "${updateTagDto.name}" đã tồn tại`,
          );
        }
      }

      // Cập nhật tag
      const updatedTag = await this.tagRepository.update(tenantId, id, updateTagDto);
      if (!updatedTag) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_UPDATE_FAILED,
          'Cập nhật tag thất bại',
        );
      }

      return updatedTag;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật tag: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_UPDATE_FAILED,
        `Cập nhật tag thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa tag
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID của tag
   * @returns true nếu xóa thành công
   */
  async deleteTag(tenantId: string, id: number): Promise<boolean> {
    try {
      // Kiểm tra tag tồn tại
      const existingTag = await this.tagRepository.findById(tenantId, id);
      if (!existingTag) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_NOT_FOUND,
          `Không tìm thấy tag với ID ${id}`,
        );
      }

      // Xóa tag
      const deleted = await this.tagRepository.delete(tenantId, id);
      if (!deleted) {
        throw new AppException(
          TODOLISTS_ERROR_CODES.TAG_DELETE_FAILED,
          'Xóa tag thất bại',
        );
      }

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa tag: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_DELETE_FAILED,
        `Xóa tag thất bại: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều tag
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID các tag cần xóa
   * @returns Kết quả xóa nhiều tag
   */
  async bulkDeleteTags(
    tenantId: string,
    ids: number[],
  ): Promise<BulkDeleteTagResponseDto> {
    try {
      const totalRequested = ids.length;
      const successIds: number[] = [];
      const failureIds: number[] = [];

      // Xóa từng tag và theo dõi kết quả
      for (const id of ids) {
        try {
          const deleted = await this.tagRepository.delete(tenantId, id);
          if (deleted) {
            successIds.push(id);
          } else {
            failureIds.push(id);
          }
        } catch (error) {
          failureIds.push(id);
          this.logger.warn(`Không thể xóa tag với ID ${id}: ${error.message}`);
        }
      }

      return {
        totalRequested,
        successCount: successIds.length,
        failureCount: failureIds.length,
        successIds,
        failureIds,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều tag: ${error.message}`, error.stack);
      throw new AppException(
        TODOLISTS_ERROR_CODES.TAG_BULK_DELETE_FAILED,
        `Xóa nhiều tag thất bại: ${error.message}`,
      );
    }
  }
}
