import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của presigned URL upload
 */
export class UploadUrlResponseDto {
  /**
   * URL tạm thời để upload file
   * @example "https://storage.example.com/presigned-url"
   */
  @ApiProperty({
    description: 'URL tạm thời để upload file',
    example: 'https://storage.example.com/presigned-url',
  })
  uploadUrl: string;

  /**
   * Key của file trên S3/Cloud Storage
   * @example "todos/attachments/123/1640995200000_document.pdf"
   */
  @ApiProperty({
    description: 'Key của file trên S3/Cloud Storage',
    example: 'todos/attachments/123/1640995200000_document.pdf',
  })
  s3Key: string;

  /**
   * ID để track upload
   * @example "upload_1640995200000_abc123"
   */
  @ApiProperty({
    description: 'ID để track upload',
    example: 'upload_1640995200000_abc123',
  })
  uploadId: string;

  /**
   * Thời gian hết hạn của URL (timestamp)
   * @example 1640996100000
   */
  @ApiProperty({
    description: 'Thời gian hết hạn của URL (timestamp)',
    example: 1640996100000,
  })
  expiresAt: number;

  /**
   * Kích thước tối đa cho phép (byte)
   * @example 52428800
   */
  @ApiProperty({
    description: 'Kích thước tối đa cho phép (byte)',
    example: 52428800,
  })
  maxFileSize: number;

  /**
   * MIME type được chấp nhận
   * @example "application/pdf"
   */
  @ApiProperty({
    description: 'MIME type được chấp nhận',
    example: 'application/pdf',
  })
  acceptedMimeType: string;
}
