import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsString,
  Min,
  MaxLength,
  <PERSON>E<PERSON>,
  <PERSON>,
} from 'class-validator';
import { MediaType } from '@shared/utils/file/media-type.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';

/**
 * D<PERSON> cho tạo presigned URL để upload tệp đính kèm
 */
export class CreateUploadUrlDto {
  /**
   * ID của công việc
   * @example 1
   */
  @ApiProperty({
    description: 'ID của công việc',
    example: 1,
    required: true,
  })
  @IsNotEmpty({ message: 'ID công việc không được để trống' })
  @IsInt({ message: 'ID công việc phải là số nguyên' })
  @Min(1, { message: 'ID công việc phải lớn hơn 0' })
  todoId: number;

  /**
   * Tên tệp đính kèm
   * @example "tài liệu dự án.pdf"
   */
  @ApiProperty({
    description: 'Tên tệp đính kèm',
    example: 'tài liệu dự án.pdf',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên tệp không được để trống' })
  @IsString({ message: 'Tên tệp phải là chuỗi' })
  @MaxLength(255, { message: 'Tên tệp không được vượt quá 255 ký tự' })
  fileName: string;

  /**
   * Loại nội dung của tệp (MIME type)
   * @example "application/pdf"
   */
  @ApiProperty({
    description: 'Loại nội dung của tệp (MIME type)',
    example: 'application/pdf',
    required: true,
  })
  @IsNotEmpty({ message: 'Loại nội dung không được để trống' })
  @IsString({ message: 'Loại nội dung phải là chuỗi' })
  @MaxLength(100, { message: 'Loại nội dung không được vượt quá 100 ký tự' })
  mimeType: string;

  /**
   * Kích thước tệp (byte)
   * @example 1024000
   */
  @ApiProperty({
    description: 'Kích thước tệp (byte)',
    example: 1024000,
    required: true,
  })
  @IsNotEmpty({ message: 'Kích thước tệp không được để trống' })
  @IsInt({ message: 'Kích thước phải là số nguyên' })
  @Min(1, { message: 'Kích thước phải lớn hơn 0' })
  @Max(FileSizeEnum.FIFTY_MB, { message: 'Kích thước tệp không được vượt quá 50MB' })
  fileSize: number;
}
