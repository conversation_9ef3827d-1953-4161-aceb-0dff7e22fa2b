import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  CommentDto,
  CommentQueryDto,
  CreateCommentDto,
  CreateSystemEventDto,
  CreateTodoCommentDto,
  TodoCommentQueryDto,
  TodoCommentResponseDto,
  UpdateCommentDto,
} from '../types/comment.types';

/**
 * Service for todo comments (new API)
 */
export const TodoCommentService = {
  /**
   * Get all comments with pagination
   * @param params Query parameters
   * @returns Promise with API response containing comments
   */
  getAllComments: (params?: TodoCommentQueryDto) => {
    return apiClient.get<PaginatedResult<TodoCommentResponseDto>>('/todo-comments', {
      params,
    });
  },

  /**
   * Get comments for a specific todo
   * @param todoId Todo ID
   * @returns Promise with API response containing comments
   */
  getCommentsByTodoId: (todoId: number) => {
    return apiClient.get<TodoCommentResponseDto[]>(`/todo-comments/by-todo/${todoId}`);
  },

  /**
   * Get a comment by ID
   * @param commentId Comment ID
   * @returns Promise with API response containing the comment
   */
  getCommentById: (commentId: number) => {
    return apiClient.get<TodoCommentResponseDto>(`/todo-comments/${commentId}`);
  },

  /**
   * Get replies for a comment
   * @param commentId Parent comment ID
   * @returns Promise with API response containing replies
   */
  getCommentReplies: (commentId: number) => {
    return apiClient.get<TodoCommentResponseDto[]>(`/todo-comments/${commentId}/replies`);
  },

  /**
   * Create a new comment
   * @param data Comment data
   * @returns Promise with API response containing the created comment
   */
  createComment: (data: CreateTodoCommentDto) => {
    return apiClient.post<TodoCommentResponseDto>('/todo-comments', data);
  },

  /**
   * Get image upload URL for comment
   * @param data Image upload request data
   * @returns Promise with API response containing upload URL
   */
  getImageUploadUrl: (data: {
    todoId: number;
    fileName: string;
    mimeType: string;
    fileSize: number;
    description?: string;
  }) => {
    return apiClient.post<string>('/todo-comments/image-upload-url', data);
  },

  /**
   * Create a system event
   * @param data System event data
   * @returns Promise with API response containing the created system event
   */
  createSystemEvent: (data: CreateSystemEventDto) => {
    return apiClient.post<TodoCommentResponseDto>('/todo-comments/system-event', data);
  },

  /**
   * Delete a comment
   * @param commentId Comment ID
   * @returns Promise with API response containing the result
   */
  deleteComment: (commentId: number) => {
    return apiClient.delete<null>(`/todo-comments/${commentId}`);
  },
};

/**
 * Service for comments (legacy API - for backward compatibility)
 */
export const CommentService = {
  /**
   * Get comments for a task
   * @param taskId Task ID
   * @param params Query parameters
   * @returns Promise with API response containing comments
   */
  getComments: (taskId: number, params?: CommentQueryDto) => {
    return apiClient.get<PaginatedResult<CommentDto>>(`/tasks/${taskId}/comments`, { params });
  },

  /**
   * Get a comment by ID
   * @param taskId Task ID
   * @param commentId Comment ID
   * @returns Promise with API response containing the comment
   */
  getComment: (taskId: number, commentId: number) => {
    return apiClient.get<CommentDto>(`/tasks/${taskId}/comments/${commentId}`);
  },

  /**
   * Create a new comment
   * @param data Comment data
   * @returns Promise with API response containing the created comment
   */
  createComment: (data: CreateCommentDto) => {
    return apiClient.post<CommentDto>(`/tasks/${data.taskId}/comments`, data);
  },

  /**
   * Update a comment
   * @param taskId Task ID
   * @param commentId Comment ID
   * @param data Updated comment data
   * @returns Promise with API response containing the updated comment
   */
  updateComment: (taskId: number, commentId: number, data: UpdateCommentDto) => {
    return apiClient.patch<CommentDto>(`/tasks/${taskId}/comments/${commentId}`, data);
  },

  /**
   * Delete a comment
   * @param taskId Task ID
   * @param commentId Comment ID
   * @returns Promise with API response containing the result
   */
  deleteComment: (taskId: number, commentId: number) => {
    return apiClient.delete<boolean>(`/tasks/${taskId}/comments/${commentId}`);
  },
};
