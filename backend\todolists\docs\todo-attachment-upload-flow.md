# Todo Attachment Upload Flow với Presigned URL

## T<PERSON><PERSON> quan
Hệ thống sử dụng pattern presigned URL để upload tệp đ<PERSON>h kèm cho công việc, tương tự như các module kh<PERSON><PERSON> trong hệ thống.

## Flow Upload

### 1. Tạo Presigned URL
```
POST /todo-attachments/upload-url
```

**Request Body:**
```json
{
  "todoId": 123,
  "fileName": "document.pdf",
  "mimeType": "application/pdf",
  "fileSize": 1024000
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đ<PERSON> tạo presigned URL thành công",
  "data": {
    "uploadUrl": "https://storage.example.com/presigned-url",
    "s3Key": "todos/attachments/123/1640995200000_document.pdf",
    "uploadId": "upload_1640995200000_abc123",
    "expiresAt": 1640996100000,
    "maxFileSize": 52428800,
    "acceptedMimeType": "application/pdf"
  }
}
```

### 2. Upload File lên Cloud Storage
Frontend sử dụng `uploadUrl` để upload file trực tiếp lên cloud storage:

```javascript
const response = await fetch(uploadUrl, {
  method: 'PUT',
  body: file,
  headers: {
    'Content-Type': mimeType
  }
});
```

### 3. Xác nhận Upload thành công
```
POST /todo-attachments/confirm-upload
```

**Request Body:**
```json
{
  "todoId": 123,
  "s3Key": "todos/attachments/123/1640995200000_document.pdf",
  "fileName": "document.pdf",
  "contentType": "application/pdf",
  "size": 1024000,
  "uploadId": "upload_1640995200000_abc123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đã xác nhận upload và lưu tệp đính kèm thành công",
  "data": {
    "id": 456,
    "todoId": 123,
    "filename": "document.pdf",
    "url": "https://cdn.example.com/download-url",
    "contentType": "application/pdf",
    "size": 1024000,
    "createdAt": 1640995200000,
    "createdBy": 1
  }
}
```

## Validation & Limits

- **File Size**: Tối đa 50MB
- **URL Expiry**: Presigned URL có hiệu lực 15 phút
- **Permissions**: Chỉ thành viên có quyền truy cập công việc mới có thể upload
- **File Types**: Hỗ trợ tất cả MIME types

## Error Handling

### Lỗi thường gặp:
- `TODO_NOT_FOUND`: Không tìm thấy công việc
- `ATTACHMENT_CREATION_FAILED`: Lỗi tạo presigned URL hoặc lưu database
- `FILE_TOO_LARGE`: File vượt quá 50MB

## Legacy API

API cũ vẫn được hỗ trợ để backward compatibility:
```
POST /todo-attachments
```

Tuy nhiên, khuyến khích sử dụng flow mới với presigned URL để tối ưu performance và bảo mật.

## Frontend Implementation Example

```typescript
// 1. Tạo presigned URL
const uploadResponse = await fetch('/todo-attachments/upload-url', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    todoId: 123,
    fileName: file.name,
    mimeType: file.type,
    fileSize: file.size
  })
});

const { uploadUrl, s3Key } = uploadResponse.data;

// 2. Upload file
await fetch(uploadUrl, {
  method: 'PUT',
  body: file,
  headers: {
    'Content-Type': file.type
  }
});

// 3. Xác nhận upload
const confirmResponse = await fetch('/todo-attachments/confirm-upload', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    todoId: 123,
    s3Key,
    fileName: file.name,
    contentType: file.type,
    size: file.size
  })
});
```
