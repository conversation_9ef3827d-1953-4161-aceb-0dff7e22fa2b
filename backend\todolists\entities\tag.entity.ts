import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity đại diện cho các nhãn/tag
 */
@Entity('tags')
export class Tag {
  /**
   * ID duy nhất của tag
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Tên của tag
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * ID của tenant/công ty sở hữu tag này
   */
  @Column({ name: 'tenant_id', type: 'bigint', nullable: true })
  tenantId: string | null;
}
