import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { TodoService } from '../services/todo.service';
import { TodoTagService } from '../services/todo-tag.service';
import { CreateTodoDto } from '../dto/todo/create-todo.dto';
import { UpdateTodoDto } from '../dto/todo/update-todo.dto';
import { TodoQueryDto } from '../dto/todo/todo-query.dto';
import { TodoResponseDto } from '../dto/todo/todo-response.dto';
import { UpdateTodoStatusDto } from '../dto/todo/update-todo-status.dto';
import { BulkDeleteTodoDto } from '../dto/todo/bulk-delete-todo.dto';
import { BulkDeleteTodoResponseDto } from '../dto/todo/bulk-delete-todo-response.dto';
import { CreateTodoTagDto } from '../dto/todo-tag/create-todo-tag.dto';
import { TodoTagResponseDto } from '../dto/todo-tag/todo-tag-response.dto';
import { ScoreTodoDto } from '../dto/todo-score/score-todo.dto';
import { TodoScoreResponseDto } from '../dto/todo-score/todo-score-response.dto';

/**
 * Controller xử lý các API liên quan đến công việc
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(
  ApiResponseDto,
  TodoResponseDto,
  TodoTagResponseDto,
  TodoScoreResponseDto,
  BulkDeleteTodoResponseDto,
)
@Controller('api/todos')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TodoController {
  constructor(
    private readonly todoService: TodoService,
    private readonly todoTagService: TodoTagService,
  ) {}

  /**
   * Tạo công việc mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo công việc mới' })
  @ApiResponse({
    status: 201,
    description: 'Công việc đã được tạo thành công',
    schema: ApiResponseDto.getSchema(TodoResponseDto),
  })
  async createTodo(
    @CurrentUser() user: JwtPayload,
    @Body() createTodoDto: CreateTodoDto,
  ): Promise<ApiResponseDto<TodoResponseDto>> {
    const todo = await this.todoService.createTodo(
      Number(user.tenantId),
      user.id,
      createTodoDto,
    );
    return ApiResponseDto.created(todo);
  }

  /**
   * Lấy danh sách công việc
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách công việc với tags' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách công việc bao gồm thông tin tags',
    schema: ApiResponseDto.getPaginatedSchema(TodoResponseDto),
  })
  async findAllTodos(
    @CurrentUser() user: JwtPayload,
    @Query() query: TodoQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TodoResponseDto>>> {
    const paginatedTodos = await this.todoService.findAllTodos(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(paginatedTodos);
  }

  /**
   * Lấy chi tiết công việc
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết công việc',
    schema: ApiResponseDto.getSchema(TodoResponseDto),
  })
  async findTodoById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoResponseDto>> {
    const todo = await this.todoService.findTodoById(Number(user.tenantId), id);
    return ApiResponseDto.success(todo);
  }

  /**
   * Lấy danh sách công việc con
   */
  @Get(':id/subtasks')
  @ApiOperation({ summary: 'Lấy danh sách công việc con' })
  @ApiParam({ name: 'id', description: 'ID công việc cha', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách công việc con',
    schema: ApiResponseDto.getPaginatedSchema(TodoResponseDto),
  })
  async findSubtasks(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Query() query: TodoQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TodoResponseDto>>> {
    const paginatedSubtasks = await this.todoService.findSubtasks(
      Number(user.tenantId),
      id,
      query,
    );
    return ApiResponseDto.paginated(paginatedSubtasks);
  }

  /**
   * Cập nhật công việc
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Công việc đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(TodoResponseDto),
  })
  async updateTodo(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Body() updateTodoDto: UpdateTodoDto,
  ): Promise<ApiResponseDto<TodoResponseDto>> {
    const todo = await this.todoService.updateTodo(
      Number(user.tenantId),
      id,
      user.id,
      updateTodoDto,
    );
    if (!todo) {
      throw new Error(`Không tìm thấy công việc với ID ${id}`);
    }
    return ApiResponseDto.success(todo);
  }

  /**
   * Cập nhật trạng thái công việc
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Cập nhật trạng thái công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái công việc đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(TodoResponseDto),
  })
  async updateTodoStatus(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Body() updateTodoStatusDto: UpdateTodoStatusDto,
  ): Promise<ApiResponseDto<TodoResponseDto>> {
    const todo = await this.todoService.updateTodoStatus(
      Number(user.tenantId),
      id,
      user.id,
      updateTodoStatusDto,
    );
    if (!todo) {
      throw new Error(`Không tìm thấy công việc với ID ${id}`);
    }
    return ApiResponseDto.success(todo);
  }

  /**
   * Xóa công việc
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Công việc đã được xóa thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async deleteTodo(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.todoService.deleteTodo(Number(user.tenantId), id, user.id);
    return ApiResponseDto.deleted();
  }

  /**
   * Xóa nhiều công việc
   */
  @Delete('bulk')
  @ApiOperation({ summary: 'Xóa nhiều công việc' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa nhiều công việc',
    schema: ApiResponseDto.getSchema(BulkDeleteTodoResponseDto),
  })
  async bulkDeleteTodos(
    @Body() bulkDeleteDto: BulkDeleteTodoDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BulkDeleteTodoResponseDto>> {
    const result = await this.todoService.bulkDeleteTodos(
      Number(user.tenantId),
      user.id,
      bulkDeleteDto,
    );
    return ApiResponseDto.success(result, 'Xóa nhiều công việc hoàn tất');
  }

  /**
   * Tạo công việc con
   */
  @Post(':id/subtasks')
  @ApiOperation({ summary: 'Tạo công việc con' })
  @ApiParam({ name: 'id', description: 'ID công việc cha', type: 'number' })
  @ApiResponse({
    status: 201,
    description: 'Công việc con đã được tạo thành công',
    schema: ApiResponseDto.getSchema(TodoResponseDto),
  })
  async createSubtask(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Body() createTodoDto: CreateTodoDto,
  ): Promise<ApiResponseDto<TodoResponseDto>> {
    // Gán parentId từ đường dẫn
    const subtaskDto = { ...createTodoDto, parentId: id };
    const todo = await this.todoService.createTodo(
      Number(user.tenantId),
      user.id,
      subtaskDto,
    );
    return ApiResponseDto.created(todo);
  }

  /**
   * Thêm tag vào công việc
   */
  @Post(':id/tags')
  @ApiOperation({ summary: 'Thêm tag vào công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 201,
    description: 'Tag đã được thêm vào công việc thành công',
    schema: ApiResponseDto.getSchema(TodoTagResponseDto),
  })
  async addTagToTodo(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Body() createTodoTagDto: CreateTodoTagDto,
  ): Promise<ApiResponseDto<TodoTagResponseDto>> {
    const tag = await this.todoTagService.addTagToTodo(
      Number(user.tenantId),
      id,
      user.id,
      createTodoTagDto,
    );
    return ApiResponseDto.created(tag);
  }

  /**
   * Lấy danh sách tag của công việc
   */
  @Get(':id/tags')
  @ApiOperation({ summary: 'Lấy danh sách tag của công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tag của công việc',
    schema: ApiResponseDto.getArraySchema(TodoTagResponseDto),
  })
  async findTagsByTodoId(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoTagResponseDto[]>> {
    const tags = await this.todoTagService.findTagsByTodoId(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(tags);
  }

  /**
   * Xóa tag khỏi công việc
   */
  @Delete(':id/tags/:tagId')
  @ApiOperation({ summary: 'Xóa tag khỏi công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiParam({ name: 'tagId', description: 'ID tag', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Tag đã được xóa khỏi công việc thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async removeTagFromTodo(
    @Param('id', ParseIntPipe) id: number,
    @Param('tagId', ParseIntPipe) tagId: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.todoTagService.removeTagFromTodo(
      Number(user.tenantId),
      id,
      tagId,
      user.id,
    );
    return ApiResponseDto.deleted();
  }

  /**
   * Chấm điểm cho công việc
   */
  @Patch(':id/score')
  @ApiOperation({ summary: 'Chấm điểm cho công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Công việc đã được chấm điểm thành công',
    schema: ApiResponseDto.getSchema(TodoResponseDto),
  })
  async scoreTodo(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
    @Body() scoreTodoDto: ScoreTodoDto,
  ): Promise<ApiResponseDto<TodoResponseDto>> {
    const todo = await this.todoService.scoreTodo(
      Number(user.tenantId),
      id,
      user.id,
      scoreTodoDto,
    );
    if (!todo) {
      throw new Error(`Không tìm thấy công việc với ID ${id}`);
    }
    return ApiResponseDto.success(todo);
  }

  /**
   * Lấy lịch sử chấm điểm của công việc
   */
  @Get(':id/scores')
  @ApiOperation({ summary: 'Lấy lịch sử chấm điểm của công việc' })
  @ApiParam({ name: 'id', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Lịch sử chấm điểm của công việc',
    schema: ApiResponseDto.getArraySchema(TodoScoreResponseDto),
  })
  async getTodoScoreHistory(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoScoreResponseDto[]>> {
    const scores = await this.todoService.getTodoScoreHistory(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(scores);
  }
}
