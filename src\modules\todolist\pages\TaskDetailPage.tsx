import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';

import { Button, Typography } from '@/shared/components/common';
import CollapsibleCard from '@/shared/components/common/CollapsibleCard';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';

import AttachmentList from '../components/attachments/AttachmentList';
import LinkKeyResultForm from '../components/okr/LinkKeyResultForm';
import TaskKeyResultList from '../components/okr/TaskKeyResultList';
import { TaskDetailHeader, TaskDetailInfo, TaskDetailSidebar } from '../components/task-detail';
import SubtasksSection from '../components/task-detail/SubtasksSection';
// Import TodoCommentsSection
import TodoCommentsSection from '../components/task-detail/TodoCommentsSection';
import TaskForm from '../components/TaskForm';
import { useAttachments } from '../hooks/useAttachments';
import { useCreateTodoComment, useTodoCommentsByTodoId } from '../hooks/useComments';
import { useSubtasks, useTask, useUpdateTaskStatus, useUpdateTask, useUpdateTaskAssignee } from '../hooks/useTasks';
import { TaskPriority, TaskStatus } from '../types/task.types';
import { CommentType } from '../types/comment.types';

/**
 * Task detail page
 */
const TaskDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const taskId = parseInt(id || '0', 10);
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();

  const {
    isVisible: isSubtaskFormOpen,
    showForm: showSubtaskForm,
    hideForm: hideSubtaskForm,
  } = useSlideForm();
  const {
    isVisible: isKeyResultFormOpen,
    showForm: showKeyResultForm,
    hideForm: hideKeyResultForm,
  } = useSlideForm();

  // Fetch task details
  const { data: task, isLoading, refetch } = useTask(taskId);

  // Fetch subtasks
  const { data: subtasksData, isLoading: isLoadingSubtasks } = useSubtasks(taskId);

  // Fetch comments
  const { data: todoCommentsData, isLoading: isLoadingTodoComments } =
    useTodoCommentsByTodoId(taskId);

  // Fetch attachments
  const { data: attachmentsData, isLoading: isLoadingAttachments } = useAttachments(taskId);

  // Mutations
  const { mutateAsync: updateTaskStatus } = useUpdateTaskStatus();
  const { mutateAsync: updateTaskAssignee } = useUpdateTaskAssignee();
  const { mutate: createTodoComment, isPending: isCreatingComment } = useCreateTodoComment();

  // Handle subtask form submit
  const handleSubtaskSubmit = () => {
    hideSubtaskForm();
    refetch();
  };

  // Handle key result form submit
  const handleKeyResultSubmit = () => {
    hideKeyResultForm();
    refetch();
  };

  // Handle back button click
  const handleBack = () => {
    navigate('/todolist/tasks');
  };

  // Handle mark complete
  const handleMarkComplete = async () => {
    if (!task) {return;}

    try {
      const newStatus =
        task.status === TaskStatus.COMPLETED ? TaskStatus.PENDING : TaskStatus.COMPLETED;

      await updateTaskStatus({ id: taskId, data: { status: newStatus } });
      NotificationUtil.success({
        message: t(
          'todolist:task.notifications.statusUpdateSuccess',
          'Task status updated successfully'
        ),
      });
      refetch();
    } catch (error) {
      console.error('Error updating task status:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.statusUpdateError', 'Error updating task status'),
      });
    }
  };

  // Handle status update
  const handleUpdateStatus = async (status: TaskStatus) => {
    try {
      await updateTaskStatus({ id: taskId, data: { status } });
      NotificationUtil.success({
        message: t(
          'todolist:task.notifications.statusUpdateSuccess',
          'Task status updated successfully'
        ),
      });
      refetch();
    } catch (error) {
      console.error('Error updating task status:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.statusUpdateError', 'Error updating task status'),
      });
    }
  };

  // Handle priority update
  const handleUpdatePriority = async (priority: TaskPriority) => {
    // TODO: Implement priority update API
    console.log('Update priority:', priority);
    NotificationUtil.success({
      message: 'Priority updated successfully',
    });
  };

  // Handle assignee update
  const handleUpdateAssignee = async (assigneeId: string) => {
    try {
      if (!task) return;

      await updateTaskAssignee({
        id: task.id,
        data: { assigneeId: parseInt(assigneeId, 10) },
      });

      NotificationUtil.success({
        message: t('todolist:task.notifications.assigneeUpdateSuccess', 'Assignee updated successfully'),
      });
    } catch (error) {
      console.error('Error updating assignee:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.assigneeUpdateError', 'Failed to update assignee'),
      });
    }
  };

  // Handle description update
  const handleUpdateDescription = async (description: string) => {
    // TODO: Implement description update API
    console.log('Update description:', description);
    NotificationUtil.success({
      message: t('todolist:task.notifications.descriptionUpdateSuccess', 'Description updated successfully'),
    });
  };

  // Handle expected stars update
  const handleUpdateExpectedStars = async (stars: number) => {
    // TODO: Implement expected stars update API
    console.log('Update expected stars:', stars);
    NotificationUtil.success({
      message: 'Expected stars updated successfully',
    });
  };

  // Handle add todo comment
  const handleAddTodoComment = (contentHtml: string, resources?: any[]) => {
    console.log('Creating todo comment:', {
      todoId: taskId,
      contentHtml,
      commentType: CommentType.NOTE,
      resources,
    });

    createTodoComment(
      {
        todoId: taskId,
        contentHtml,
        commentType: CommentType.NOTE,
        resources,
      },
      {
        onSuccess: (data) => {
          console.log('Comment created successfully:', data);
          NotificationUtil.success({
            message: t(
              'todolist:task.comments.notifications.createSuccess',
              'Comment added successfully'
            ),
          });
        },
        onError: (error) => {
          console.error('Error adding todo comment:', error);
          NotificationUtil.error({
            message: t('todolist:task.comments.notifications.createError', 'Error adding comment'),
          });
        },
      }
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg text-gray-500">{t('common:loading', 'Loading...')}</div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="text-center py-8">
        <Typography variant="h4" className="mb-2">
          {t('todolist:task.notFound', 'Task not found')}
        </Typography>
        <Button onClick={handleBack} className="mt-4">
          {t('common:back', 'Back')}
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header */}
      <TaskDetailHeader
        title={task.title}
        onBack={handleBack}
        onReload={() => refetch()}
        onMarkComplete={handleMarkComplete}
        isCompleted={task.status === TaskStatus.COMPLETED}
      />

      {/* Add Subtask Form */}
      <SlideInForm isVisible={isSubtaskFormOpen}>
        <TaskForm parentId={taskId} onSubmit={handleSubtaskSubmit} onCancel={hideSubtaskForm} />
      </SlideInForm>

      {/* Link Key Result Form */}
      <SlideInForm isVisible={isKeyResultFormOpen}>
        <LinkKeyResultForm
          taskId={taskId}
          onSubmit={handleKeyResultSubmit}
          onCancel={hideKeyResultForm}
        />
      </SlideInForm>

      {/* Task Details Section */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-semibold">
            {t('todolist:task.sections.details', 'Task Details')}
          </Typography>
        }
        defaultOpen={true}
      >
        <TaskDetailInfo task={task} onUpdateDescription={handleUpdateDescription} />
      </CollapsibleCard>

      {/* Properties Section */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-semibold">
            {t('todolist:task.sections.properties', 'Properties')}
          </Typography>
        }
        defaultOpen={true}
      >
        <TaskDetailSidebar
          task={task}
          onUpdateStatus={handleUpdateStatus}
          onUpdatePriority={handleUpdatePriority}
          onUpdateAssignee={handleUpdateAssignee}
          onUpdateExpectedStars={handleUpdateExpectedStars}
          onRefresh={() => refetch()}
        />
      </CollapsibleCard>

      {/* Attachments Section */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-semibold">
            {t('todolist:attachments.title', 'Attachments')} (
            {attachmentsData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <AttachmentList
          taskId={taskId}
          attachments={attachmentsData?.items || []}
          isLoading={isLoadingAttachments}
          onRefresh={refetch}
        />
      </CollapsibleCard>

      {/* Subtasks Section */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-semibold">
            {t('todolist:task.sections.subtasks', 'Subtasks')} ({subtasksData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <div className="h-96">
          <SubtasksSection
            taskId={taskId}
            subtasks={subtasksData?.items || []}
            isLoading={isLoadingSubtasks}
            onAddSubtask={showSubtaskForm}
            onRefresh={refetch}
          />
        </div>
      </CollapsibleCard>

      {/* Key Results Section */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-semibold">
            {t('todolist:task.sections.keyResults', 'Key Results')}
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <TaskKeyResultList taskId={taskId} onAddKeyResult={showKeyResultForm} />
      </CollapsibleCard>

      {/* Comments Section */}
      <CollapsibleCard
        title={
          <Typography variant="h6" className="font-semibold">
            {t('todolist:task.sections.comments', 'Comments')} ({todoCommentsData?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <div className="h-100">
          <TodoCommentsSection
            taskId={taskId}
            comments={todoCommentsData || []}
            isLoading={isLoadingTodoComments}
            onAddComment={handleAddTodoComment}
            onRefresh={refetch}
            isSubmitting={isCreatingComment}
          />
        </div>
      </CollapsibleCard>
      <div className="h-30"></div>
    </div>
  );
};

export default TaskDetailPage;
