import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import {
  ApiResponseDto,
  PaginatedResult,
} from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { TodoAttachmentService } from '../services/todo-attachment.service';
import { CreateTodoAttachmentDto } from '../dto/todo-attachment/create-todo-attachment.dto';
import { CreateUploadUrlDto } from '../dto/todo-attachment/create-upload-url.dto';
import { UploadUrlResponseDto } from '../dto/todo-attachment/upload-url-response.dto';
import { ConfirmUploadDto } from '../dto/todo-attachment/confirm-upload.dto';
import { TodoAttachmentQueryDto } from '../dto/todo-attachment/todo-attachment-query.dto';
import { TodoAttachmentResponseDto } from '../dto/todo-attachment/todo-attachment-response.dto';

/**
 * Controller xử lý các API liên quan đến tệp đính kèm công việc
 */
@ApiTags(SWAGGER_API_TAG.TODOLISTS)
@ApiExtraModels(ApiResponseDto, TodoAttachmentResponseDto, UploadUrlResponseDto)
@Controller('/todo-attachments')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class TodoAttachmentController {
  constructor(private readonly todoAttachmentService: TodoAttachmentService) {}

  /**
   * Tạo presigned URL để upload tệp đính kèm
   */
  @Post('upload-url')
  @ApiOperation({ summary: 'Tạo presigned URL để upload tệp đính kèm' })
  @ApiResponse({
    status: 201,
    description: 'Presigned URL đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UploadUrlResponseDto),
  })
  async createUploadUrl(
    @CurrentUser() user: JwtPayload,
    @Body() createUploadDto: CreateUploadUrlDto,
  ): Promise<ApiResponseDto<UploadUrlResponseDto>> {
    const uploadInfo = await this.todoAttachmentService.createUploadUrl(
      Number(user.tenantId),
      user.id,
      createUploadDto,
    );
    return ApiResponseDto.created(
      uploadInfo,
      'Đã tạo presigned URL thành công',
    );
  }

  /**
   * Xác nhận upload thành công và lưu thông tin vào database
   */
  @Post('confirm-upload')
  @ApiOperation({ summary: 'Xác nhận upload thành công và lưu thông tin vào database' })
  @ApiResponse({
    status: 201,
    description: 'Upload đã được xác nhận và lưu thành công',
    schema: ApiResponseDto.getSchema(TodoAttachmentResponseDto),
  })
  async confirmUpload(
    @CurrentUser() user: JwtPayload,
    @Body() confirmUploadDto: ConfirmUploadDto,
  ): Promise<ApiResponseDto<TodoAttachmentResponseDto>> {
    const attachment = await this.todoAttachmentService.confirmUpload(
      Number(user.tenantId),
      user.id,
      confirmUploadDto,
    );
    return ApiResponseDto.created(
      attachment,
      'Đã xác nhận upload và lưu tệp đính kèm thành công',
    );
  }

  /**
   * Thêm tệp đính kèm cho công việc (Legacy - sử dụng upload-url và confirm-upload thay thế)
   */
  @Post()
  @ApiOperation({ summary: 'Thêm tệp đính kèm cho công việc (Legacy)' })
  @ApiResponse({
    status: 201,
    description: 'Tệp đính kèm đã được thêm thành công',
    schema: ApiResponseDto.getSchema(TodoAttachmentResponseDto),
  })
  async addAttachment(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateTodoAttachmentDto,
  ): Promise<ApiResponseDto<TodoAttachmentResponseDto>> {
    const attachment = await this.todoAttachmentService.addAttachment(
      Number(user.tenantId),
      user.id,
      createDto,
    );
    return ApiResponseDto.created(
      attachment,
      'Đã thêm tệp đính kèm thành công',
    );
  }

  /**
   * Lấy danh sách tệp đính kèm
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tệp đính kèm' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tệp đính kèm',
    schema: ApiResponseDto.getPaginatedSchema(TodoAttachmentResponseDto),
  })
  async findAll(
    @CurrentUser() user: JwtPayload,
    @Query() query: TodoAttachmentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<TodoAttachmentResponseDto>>> {
    const paginatedAttachments = await this.todoAttachmentService.findAll(
      Number(user.tenantId),
      query,
    );
    return ApiResponseDto.paginated(
      paginatedAttachments,
      'Lấy danh sách tệp đính kèm thành công',
    );
  }

  /**
   * Lấy danh sách tệp đính kèm của một công việc
   */
  @Get('by-todo/:todoId')
  @ApiOperation({ summary: 'Lấy danh sách tệp đính kèm của một công việc' })
  @ApiParam({ name: 'todoId', description: 'ID công việc', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tệp đính kèm của công việc',
    schema: ApiResponseDto.getArraySchema(TodoAttachmentResponseDto),
  })
  async findByTodoId(
    @CurrentUser() user: JwtPayload,
    @Param('todoId', ParseIntPipe) todoId: number,
  ): Promise<ApiResponseDto<TodoAttachmentResponseDto[]>> {
    const attachments = await this.todoAttachmentService.findByTodoId(
      Number(user.tenantId),
      todoId,
    );
    return ApiResponseDto.success(
      attachments,
      'Lấy danh sách tệp đính kèm của công việc thành công',
    );
  }

  /**
   * Lấy chi tiết tệp đính kèm
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết tệp đính kèm' })
  @ApiParam({ name: 'id', description: 'ID tệp đính kèm', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết tệp đính kèm',
    schema: ApiResponseDto.getSchema(TodoAttachmentResponseDto),
  })
  async findById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<TodoAttachmentResponseDto>> {
    const attachment = await this.todoAttachmentService.findById(
      Number(user.tenantId),
      id,
    );
    return ApiResponseDto.success(
      attachment,
      'Lấy chi tiết tệp đính kèm thành công',
    );
  }

  /**
   * Xóa tệp đính kèm
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa tệp đính kèm' })
  @ApiParam({ name: 'id', description: 'ID tệp đính kèm', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Tệp đính kèm đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  async removeAttachment(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<null>> {
    await this.todoAttachmentService.removeAttachment(
      Number(user.tenantId),
      user.id,
      id,
    );
    return ApiResponseDto.success(null, 'Đã xóa tệp đính kèm thành công');
  }
}
