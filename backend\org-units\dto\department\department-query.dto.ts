import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO for querying departments
 */
export class DepartmentQueryDto extends QueryDto {
  /**
   * Filter by department manager
   */
  @ApiProperty({
    description: 'Lọc theo ID người quản lý phòng ban',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  managerId?: number;

  /**
   * Filter by parent department
   */
  @ApiProperty({
    description: 'Lọc theo ID phòng ban cấp trên',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  parentId?: number;
} 