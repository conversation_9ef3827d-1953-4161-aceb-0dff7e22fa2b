import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  AttachmentDto,
  AttachmentQueryDto,
  CreateAttachmentDto,
  UpdateAttachmentDto,
  TodoAttachmentResponseDto,
} from '../types/attachment.types';

/**
 * Service for attachments
 */
export const AttachmentService = {
  /**
   * Get attachments for a task
   * @param taskId Task ID
   * @param params Query parameters
   * @returns Promise with API response containing attachments
   */
  getAttachments: (taskId: number, params?: AttachmentQueryDto) => {
    return apiClient.get<PaginatedResult<AttachmentDto>>(`/tasks/${taskId}/attachments`, {  
      params,
    });
  },

  /**
   * Get an attachment by ID
   * @param taskId Task ID
   * @param attachmentId Attachment ID
   * @returns Promise with API response containing the attachment
   */
  getAttachment: (taskId: number, attachmentId: number) => {
    return apiClient.get<AttachmentDto>(`/tasks/${taskId}/attachments/${attachmentId}`);
  },

  /**
   * Create a new attachment
   * @param data Attachment data
   * @returns Promise with API response containing the created attachment
   */
  createAttachment: (data: CreateAttachmentDto) => {
    return apiClient.post<AttachmentDto>(`/tasks/${data.taskId}/attachments`, data);
  },

  /**
   * Update an attachment
   * @param taskId Task ID
   * @param attachmentId Attachment ID
   * @param data Updated attachment data
   * @returns Promise with API response containing the updated attachment
   */
  updateAttachment: (taskId: number, attachmentId: number, data: UpdateAttachmentDto) => {
    return apiClient.patch<AttachmentDto>(`/tasks/${taskId}/attachments/${attachmentId}`, data);
  },

  /**
   * Delete an attachment
   * @param taskId Task ID
   * @param attachmentId Attachment ID
   * @returns Promise with API response containing the result
   */
  deleteAttachment: (taskId: number, attachmentId: number) => {
    return apiClient.delete<boolean>(`/tasks/${taskId}/attachments/${attachmentId}`);
  },

  /**
   * Get upload URL for an attachment
   * @param taskId Task ID
   * @param fileName File name
   * @param fileType File type
   * @returns Promise with API response containing the upload URL
   */
  getUploadUrl: (taskId: number, fileName: string, fileType: string) => {
    return apiClient.post<{ uploadUrl: string; fileUrl: string }>(
      `/tasks/${taskId}/attachments/upload-url`,
      {
        fileName,
        fileType,
      }
    );
  },
};

/**
 * Service for todo attachments with new API endpoints
 */
export const TodoAttachmentService = {
  /**
   * Lấy danh sách tệp đính kèm của một công việc
   * @param todoId ID của công việc
   * @returns Promise with API response containing attachments list
   */
  getAttachmentsByTodoId: (todoId: number) => {
    return apiClient.get<TodoAttachmentResponseDto[]>(`/v1/todo-attachments/by-todo/${todoId}`);
  },

  /**
   * Tạo presigned URL để upload tệp đính kèm
   * @param todoId ID của công việc
   * @param fileName Tên tệp đính kèm
   * @param mimeType Loại nội dung của tệp (MIME type)
   * @param fileSize Kích thước tệp (byte)
   * @returns Promise with API response containing upload URL info
   */
  createUploadUrl: (todoId: number, fileName: string, mimeType: string, fileSize: number) => {
    return apiClient.post<{
      uploadUrl: string;
      s3Key: string;
      uploadId: string;
      expiresAt: number;
      maxFileSize: number;
      acceptedMimeType: string;
    }>('/todo-attachments/upload-url', {
      todoId,
      fileName,
      mimeType,
      fileSize,
    });
  },

  /**
   * Xác nhận upload thành công và lưu thông tin vào database
   * @param todoId ID của công việc
   * @param s3Key Key của file trên S3/Cloud Storage
   * @param fileName Tên tệp đính kèm
   * @param contentType Loại nội dung của tệp (MIME type)
   * @param size Kích thước tệp thực tế (byte)
   * @param uploadId ID upload để track (optional)
   * @returns Promise with API response containing attachment info
   */
  confirmUpload: (
    todoId: number,
    s3Key: string,
    fileName: string,
    contentType?: string,
    size?: number,
    uploadId?: string
  ) => {
    return apiClient.post<AttachmentDto>('/todo-attachments/confirm-upload', {
      todoId,
      s3Key,
      fileName,
      contentType,
      size,
      uploadId,
    });
  },

  /**
   * Xóa tệp đính kèm
   * @param attachmentId ID của tệp đính kèm
   * @returns Promise with API response
   */
  deleteAttachment: (attachmentId: number) => {
    return apiClient.delete<boolean>(`/v1/todo-attachments/${attachmentId}`);
  },
};
