import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department } from '../entities/department.entity';
import { DepartmentQueryDto } from '../dto/department';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository for department entity
 */
@Injectable()
export class DepartmentRepository {
  private readonly logger = new Logger(DepartmentRepository.name);

  constructor(
    @InjectRepository(Department)
    private readonly repository: Repository<Department>,
  ) {}

  /**
   * Find all departments with pagination and filtering
   * @param query Query parameters
   * @returns Paginated list of departments
   */
  async findAll(query: DepartmentQueryDto): Promise<PaginatedResult<Department>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'name',
      sortDirection = 'ASC',
      managerId,
      parentId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('department');

    // Apply filters if provided
    if (managerId) {
      queryBuilder.andWhere('department.managerId = :managerId', { managerId });
    }

    if (parentId !== undefined) {
      if (parentId === null) {
        queryBuilder.andWhere('department.parentId IS NULL');
      } else {
        queryBuilder.andWhere('department.parentId = :parentId', { parentId });
      }
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder.andWhere('department.name ILIKE :search OR department.description ILIKE :search',
        { search: `%${search}%` });
    }

    // Apply sorting
    queryBuilder.orderBy(`department.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find department by ID
   * @param id Department ID
   * @returns Department or null if not found
   */
  async findById(id: number): Promise<Department | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Find department by name
   * @param name Department name
   * @returns Department or null if not found
   */
  async findByName(name: string): Promise<Department | null> {
    return this.repository.findOne({
      where: { name },
    });
  }

  /**
   * Check if department has children
   * @param id Department ID
   * @returns True if department has children, false otherwise
   */
  async hasChildren(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { parentId: id },
    });
    return count > 0;
  }

  /**
   * Create a new department
   * @param data Department data
   * @returns Created department
   */
  async create(data: Partial<Department>): Promise<Department> {
    const department = this.repository.create(data);
    return this.repository.save(department);
  }

  /**
   * Update department
   * @param id Department ID
   * @param data Updated department data
   * @returns Updated department or null if not found
   */
  async update(id: number, data: Partial<Department>): Promise<Department | null> {
    await this.repository.update({ id }, data);
    return this.findById(id);
  }

  /**
   * Delete department
   * @param id Department ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete({ id });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Get all departments
   * @returns List of departments
   */
  async getAllByTenantId(): Promise<Department[]> {
    return this.repository.find({
      order: { name: 'ASC' },
    });
  }
} 