import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả xóa nhiều công việc
 */
export class BulkDeleteTodoResponseDto {
  /**
   * Tổng số công việc được yêu cầu xóa
   */
  @ApiProperty({
    description: 'Tổng số công việc được yêu cầu xóa',
    example: 5,
    type: Number,
  })
  totalRequested: number;

  /**
   * Số công việc đã xóa thành công
   */
  @ApiProperty({
    description: 'Số công việc đã xóa thành công',
    example: 3,
    type: Number,
  })
  successCount: number;

  /**
   * Số công việc không thể xóa
   */
  @ApiProperty({
    description: 'Số công việc không thể xóa',
    example: 2,
    type: Number,
  })
  failureCount: number;

  /**
   * <PERSON>h sách ID các công việc đã xóa thành công
   */
  @ApiProperty({
    description: '<PERSON>h sách ID các công việc đã xóa thành công',
    example: [1, 3, 5],
    type: [Number],
  })
  deletedIds: number[];

  /**
   * Danh sách các công việc không thể xóa với lý do
   */
  @ApiProperty({
    description: 'Danh sách các công việc không thể xóa với lý do',
    example: [
      { id: 2, reason: 'Không tìm thấy công việc' },
      { id: 4, reason: 'Bạn không có quyền xóa công việc này' }
    ],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        reason: { type: 'string' }
      }
    }
  })
  failures: Array<{ id: number; reason: string }>;
}
