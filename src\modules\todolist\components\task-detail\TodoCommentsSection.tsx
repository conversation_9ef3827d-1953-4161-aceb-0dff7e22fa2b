import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Typography } from '@/shared/components/common';
// Additional imports for file upload functionality
import { Icon, Tooltip } from '@/shared/components/common';
import { UploadedFile } from '@/shared/components/layout/chat-panel/FileUploadPreview';
import FileUploadPreview from '@/shared/components/layout/chat-panel/FileUploadPreview';
import UploadMenu from '@/shared/components/layout/chat-panel/UploadMenu';
import { formatTimestamp } from '@/shared/utils/form-date-utils';
import { uploadToPresignedUrl } from '@/shared/utils/upload';

import { TodoCommentResponseDto, ResourceDto } from '../../types/comment.types';
import { TodoCommentService } from '../../services/comment.service';

interface TodoCommentsSectionProps {
  taskId: number;
  comments: TodoCommentResponseDto[];
  isLoading: boolean;
  onAddComment: (contentHtml: string, resources?: ResourceDto[]) => void;
  onRefresh: () => void;
  isSubmitting?: boolean;
}

/**
 * Todo comments section component for new API
 */
const TodoCommentsSection: React.FC<TodoCommentsSectionProps> = ({
  taskId,
  comments,
  isLoading,
  onAddComment,
  onRefresh,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['todolist', 'common']);
  const [commentText, setCommentText] = useState('');
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isUploadMenuOpen, setIsUploadMenuOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto scroll to bottom when new comments are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [comments.length]);

  // Handle paste event for images from clipboard (like ChatPanel)
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      // Kiểm tra xem có đang focus vào textarea không
      if (document.activeElement !== textareaRef.current) {return;}

      const items = e.clipboardData?.items;
      if (!items) {return;}

      // Tìm kiếm file ảnh trong clipboard
      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        // Kiểm tra nếu là file ảnh
        if (item.kind === 'file' && item.type.startsWith('image/')) {
          e.preventDefault(); // Ngăn chặn hành vi paste mặc định

          const file = item.getAsFile();
          if (file) {
            // Kiểm tra giới hạn số file
            if (files.length >= 5) {
              alert(t('common:maxFilesExceeded', { max: 5 }));
              return;
            }

            // Tạo file object cho preview
            const id = `clipboard-${Date.now()}-${Math.random()}`;
            const url = URL.createObjectURL(file);

            const newFile: UploadedFile = {
              id,
              name: `clipboard-image-${Date.now()}.png`,
              type: file.type,
              url,
              isLoading: true,
            };

            // Store the actual File object for later upload
            (newFile as any).file = file;
            setFiles(prev => [...prev, newFile]);
          }
          break;
        }
      }
    };

    // Thêm event listener cho sự kiện paste
    document.addEventListener('paste', handlePaste);

    // Cleanup
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [files, taskId, t]);

  const handleSendComment = async () => {
    if (commentText.trim() || files.length > 0) {
      try {
        // For now, just send text comments without image upload
        // TODO: Implement image upload when backend API is ready
        const resources: ResourceDto[] = [];

        // Convert plain text to HTML for now
        const htmlContent = commentText.trim() ? `<p>${commentText.trim()}</p>` : '';
        onAddComment(htmlContent, resources.length > 0 ? resources : undefined);
        setCommentText('');
        setFiles([]);
      } catch (error) {
        console.error('Error sending comment:', error);
      }
    }
  };

  const handleUploadFromComputer = (fileList: FileList) => {
    // Check if adding these files would exceed the limit
    if (files.length + fileList.length > 5) {
      alert(t('common:maxFilesExceeded', { max: 5 }));
      return;
    }

    setIsUploadMenuOpen(false);

    // Convert FileList to UploadedFile array
    const newFiles: UploadedFile[] = Array.from(fileList).map(file => {
      const fileId = `file-${Date.now()}-${Math.random()}`;
      const newFile = {
        id: fileId,
        name: file.name,
        type: file.type,
        url: URL.createObjectURL(file),
        isLoading: false,
      };

      // Store the actual File object for later upload
      (newFile as any).file = file;
      return newFile;
    });

    setFiles(prev => [...prev, ...newFiles]);
  };

  const handleUploadFromGoogleDrive = () => {
    // Check if adding a file would exceed the limit
    if (files.length >= 5) {
      alert(t('common:maxFilesExceeded', { max: 5 }));
      return;
    }

    setIsUploadMenuOpen(false);

    // Mock Google Drive file for demo
    const mockFile: UploadedFile = {
      id: `gdrive-${Date.now()}`,
      name: 'Google Drive Document.pdf',
      type: 'application/pdf',
      url: '#',
      isLoading: true,
    };

    setFiles(prev => [...prev, mockFile]);

    // Simulate upload completion
    setTimeout(() => {
      setFiles(prevFiles =>
        prevFiles.map(f => (f.id === mockFile.id ? { ...f, isLoading: false } : f))
      );
    }, 2000);
  };

  const handleRemoveFile = (id: string) => {
    setFiles(files.filter(file => file.id !== id));
  };

  // Convert comment to ChatMessage format
  const convertToMessage = (comment: TodoCommentResponseDto) => {
    let content: React.ReactNode;

    if (comment.isSystemEvent && comment.eventData) {
      // System event content
      content = (
        <div className="text-blue-600 dark:text-blue-400 text-sm">
          <div className="font-medium">
            {t('todolist:task.comments.systemEvent', 'System Event')}: {comment.eventData.eventType}
          </div>
          {comment.eventData.oldValue && comment.eventData.newValue && (
            <div className="text-xs mt-1">
              {comment.eventData.oldValue} → {comment.eventData.newValue}
            </div>
          )}
        </div>
      );
    } else {
      // Regular comment content
      content = (
        <div>
          <div
            className="prose prose-sm dark:prose-invert max-w-none"
            dangerouslySetInnerHTML={{ __html: comment.contentHtml }}
          />
          {comment.resources && comment.resources.length > 0 && (
            <div className="mt-2 space-y-1">
              <div className="text-xs text-gray-500">
                {t('todolist:task.comments.attachments', 'Attachments')}:
              </div>
              {comment.resources.map((resource, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <span className="text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                    {resource.type}
                  </span>
                  <a
                    href={resource.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    {resource.name}
                  </a>
                </div>
              ))}
            </div>
          )}
          {comment.mentions && comment.mentions.length > 0 && (
            <div className="mt-2">
              <div className="text-xs text-gray-500">
                {t('todolist:task.comments.mentions', 'Mentions')}:
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {comment.mentions.map((mention, index) => (
                  <span
                    key={index}
                    className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded"
                  >
                    @{mention.username}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    }

    return {
      content,
      sender: comment.isSystemEvent ? ('ai' as const) : ('user' as const),
      timestamp: new Date(comment.createdAt || Date.now()),
    };
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          {comments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-8">
              <div className="text-4xl mb-4">💬</div>
              <Typography variant="h6" className="mb-2">
                {t('todolist:task.comments.empty', 'No comments yet')}
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                {t('todolist:task.comments.emptyDescription', 'Be the first to comment!')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map(comment => {
                const message = convertToMessage(comment);
                const isPending = (comment as any).isPending;
                return (
                  <div key={comment.id} className="mb-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {comment.isSystemEvent ? 'S' : 'U'}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {comment.isSystemEvent
                              ? 'System'
                              : `User ${comment.userId || 'Unknown'}`}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatTimestamp(comment.createdAt || 0)}
                          </span>
                          {comment.commentType && (
                            <span className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 rounded">
                              {comment.commentType}
                            </span>
                          )}
                          {isPending && (
                            <div className="flex items-center space-x-1">
                              <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                              <span className="text-xs text-blue-500">
                                {t('todolist:task.comments.sending', 'Sending...')}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className={`bg-gray-50 dark:bg-gray-800 rounded-lg p-3 ${isPending ? 'opacity-70' : ''}`}>
                          {message.content}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
              {/* Scroll anchor */}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
      </div>

      {/* Input area */}
      <div className="bg-white dark:bg-gray-800">
        <div className="p-4">
          {/* File upload preview */}
          {files.length > 0 && (
            <div className="mb-3">
              <FileUploadPreview files={files} onRemove={handleRemoveFile} />
            </div>
          )}

          {/* Main input container - unified style like ChatInputBox */}
          <div className="relative flex flex-col bg-white dark:bg-gray-800 rounded-xl shadow-lg w-full">
            {/* Text input area */}
            <div className="w-full px-3 py-3">
              <textarea
                ref={textareaRef}
                value={commentText}
                onChange={e => setCommentText(e.target.value)}
                placeholder={t('todolist:task.comments.placeholder', 'Write a comment...')}
                rows={1}
                className="w-full bg-transparent border-0 focus:ring-0 focus:outline-none dark:text-white text-gray-800 resize-none max-h-[120px]"
                onKeyDown={e => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendComment();
                  }
                }}
                onInput={e => {
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = 'auto';
                  target.style.height = `${Math.min(target.scrollHeight, 120)  }px`;
                }}
              />
            </div>

            {/* Action buttons row */}
            <div className="flex items-center px-2 py-2 space-x-1">
              {/* Upload button */}
              <div className="relative flex-shrink-0">
                <Tooltip content={t('common:uploadFile', 'Upload file')} position="top">
                  <button
                    onClick={() => setIsUploadMenuOpen(!isUploadMenuOpen)}
                    className="p-2 w-10 h-10 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
                    aria-label={t('common:uploadFile', 'Upload file')}
                  >
                    <Icon name="plus" size="md" />
                  </button>
                </Tooltip>

                {/* Upload menu */}
                {isUploadMenuOpen && (
                  <UploadMenu
                    isOpen={isUploadMenuOpen}
                    onClose={() => setIsUploadMenuOpen(false)}
                    onUploadFromComputer={handleUploadFromComputer}
                    onUploadFromGoogleDrive={handleUploadFromGoogleDrive}
                  />
                )}
              </div>

              <div className="flex-grow"></div>

              {/* Send button */}
              <Tooltip content={t('common:send', 'Send')} position="top">
                <button
                  onClick={handleSendComment}
                  disabled={(!commentText.trim() && files.length === 0) || isSubmitting}
                  className={`p-2 w-10 h-10 flex items-center justify-center rounded-full transition-colors ${
                    (commentText.trim() || files.length > 0) && !isSubmitting
                      ? 'text-white bg-primary hover:bg-primary/90'
                      : 'text-gray-400 dark:text-gray-600 cursor-not-allowed bg-gray-100 dark:bg-gray-700'
                  }`}
                  aria-label={t('common:send', 'Send')}
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Icon name="send" size="md" />
                  )}
                </button>
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TodoCommentsSection;
