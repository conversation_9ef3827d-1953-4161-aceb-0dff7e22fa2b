import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, MaxLength } from 'class-validator';

/**
 * DTO for updating a department
 */
export class UpdateDepartmentDto {
  /**
   * Name of the department
   */
  @ApiProperty({
    description: 'Tên phòng ban',
    example: 'Phòng Kỹ thuật (Đã cập nhật)',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  /**
   * Description of the department
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về phòng ban',
    example: 'Phòng ban phụ trách các vấn đề kỹ thuật và phát triển sản phẩm của công ty',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * ID of the department manager
   */
  @ApiProperty({
    description: 'ID của người quản lý phòng ban',
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  managerId?: number;

  /**
   * ID of the parent department
   */
  @ApiProperty({
    description: 'ID của phòng ban cấp trên (nếu có)',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  parentId?: number;
} 