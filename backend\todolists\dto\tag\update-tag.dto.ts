import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho cập nhật tag
 */
export class UpdateTagDto {
  /**
   * Tên của tag
   * @example "High Priority"
   */
  @ApiProperty({
    description: 'Tên của tag',
    example: 'High Priority',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên tag phải là chuỗi' })
  @MaxLength(255, { message: 'Tên tag không được vượt quá 255 ký tự' })
  name?: string;
}
