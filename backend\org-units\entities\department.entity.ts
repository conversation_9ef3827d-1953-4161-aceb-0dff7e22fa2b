import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity representing departments in the organization
 */
@Entity('departments')
export class Department {
  /**
   * Unique identifier for the department
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Department name
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Detailed description of the department
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * ID of the department manager
   */
  @Column({ name: 'manager_id', type: 'integer', nullable: true })
  managerId: number | null;

  /**
   * ID of the parent department (if any)
   */
  @Column({ name: 'parent_id', type: 'integer', nullable: true })
  parentId: number | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
