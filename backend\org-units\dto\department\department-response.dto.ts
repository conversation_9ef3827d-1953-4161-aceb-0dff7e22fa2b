import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for department response
 */
export class DepartmentResponseDto {
  /**
   * Department ID
   */
  @ApiProperty({
    description: 'ID của phòng ban',
    example: 1,
  })
  id: number;

  /**
   * Department name
   */
  @ApiProperty({
    description: 'Tên phòng ban',
    example: 'Phòng Kỹ thuật',
  })
  name: string;

  /**
   * Department description
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về phòng ban',
    example: 'Phòng ban phụ trách các vấn đề kỹ thuật của công ty',
    nullable: true,
  })
  description: string | null;

  /**
   * Department manager ID
   */
  @ApiProperty({
    description: 'ID của người quản lý phòng ban',
    example: 1,
    nullable: true,
  })
  managerId: number | null;

  /**
   * Parent department ID
   */
  @ApiProperty({
    description: 'ID của phòng ban cấp trên (nếu có)',
    example: 2,
    nullable: true,
  })
  parentId: number | null;

  /**
   * Creation timestamp
   */
  @ApiProperty({
    description: 'Thời gian tạo phòng ban (timestamp)',
    example: 1640995200000,
    nullable: true,
  })
  createdAt: number | null;
} 